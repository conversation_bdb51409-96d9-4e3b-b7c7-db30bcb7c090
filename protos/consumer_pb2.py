# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: consumer.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0e\x63onsumer.proto\x12\x06protos\"\x95\x01\n\x1cParsePythonProjectTaskResult\x12\x12\n\nproject_id\x18\x01 \x01(\t\x12\x11\n\tconfig_id\x18\x02 \x01(\t\x12\x1f\n\troot_node\x18\x03 \x01(\x0b\x32\x0c.protos.Node\x12\x17\n\x0ftrigger_account\x18\x04 \x01(\t\x12\x14\n\x0ctrigger_time\x18\x05 \x01(\x03\"\x7f\n\x04Node\x12\x0c\n\x04path\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\r\n\x05\x61lias\x18\x03 \x01(\t\x12\x1e\n\x04type\x18\x04 \x01(\x0e\x32\x10.protos.NodeType\x12\x0c\n\x04tags\x18\x05 \x03(\t\x12\x1e\n\x08\x63hildren\x18\x06 \x03(\x0b\x32\x0c.protos.Node*t\n\x08NodeType\x12\x0b\n\x07NT_NULL\x10\x00\x12\x10\n\x0cNT_DIRECTORY\x10\x01\x12\x0b\n\x07NT_FILE\x10\x02\x12\x0e\n\nNT_PACKAGE\x10\x03\x12\r\n\tNT_MODULE\x10\x04\x12\x0c\n\x08NT_CLASS\x10\x05\x12\x0f\n\x0bNT_FUNCTION\x10\x06\x62\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'consumer_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _NODETYPE._serialized_start=307
  _NODETYPE._serialized_end=423
  _PARSEPYTHONPROJECTTASKRESULT._serialized_start=27
  _PARSEPYTHONPROJECTTASKRESULT._serialized_end=176
  _NODE._serialized_start=178
  _NODE._serialized_end=305
# @@protoc_insertion_point(module_scope)
