# 使用官方的Python 3.9镜像作为基础镜像
FROM python:3.9.18

# 设置工作目录
WORKDIR /app

# 将当前目录下的所有文件复制到容器的工作目录中
COPY . /app

# 安装依赖包
#RUN pip install -i http://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com poetry
#RUN poetry config virtualenvs.create false && poetry install --no-interaction --no-ansi
RUN pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/

# 设置容器启动时执行的命令
#CMD ["poetry", "run", "python3", "consumer.py"]
CMD ["python3", "consumer.py"]