[tool.poetry]
name = "python-parse"
version = "0.1.0"
description = ""
authors = ["pangyunjin <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.9"
pydantic = "^2.4.2"
loguru = "^0.7.2"
pyyaml = "^6.0.1"
redis = "^5.0.1"
gitpython = "^3.1.40"
google = "^3.0.0"
protobuf = "^4.24.4"

[[tool.poetry.source]]
name = "aliyun"
url = "http://mirrors.aliyun.com/pypi/simple/"
# url = "https://pypi.mirrors.ustc.edu.cn/simple/"
# url = "http://pypi.douban.com/simple/"
# url = "https://pypi.tuna.tsinghua.edu.cn/simple/"
default = true


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
