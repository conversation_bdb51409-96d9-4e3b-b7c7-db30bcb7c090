import base64
import json
import os.path
import shutil
import sys
import time
import traceback
from concurrent.futures import Thr<PERSON>PoolExecutor
from pathlib import Path
from typing import Union, List
from urllib.parse import urlparse, urlunparse
from google.protobuf.json_format import Parse
import yaml
from loguru import logger

import common
import const
import git_action
from models import ConsumerConfigModel, BaseTaskModel, TaskInfoModel, ExecuteParamsModel, ParseTaskModel, \
    DispatcherTaskModel
from parser import PyParser
from redis_client import Redis
from protos import consumer_pb2


def register_logging():
    """
    注册日志句柄
    """
    log_path = Path("./logs")
    if not log_path.is_dir():
        log_path.mkdir(mode=0o755, parents=True)
    # 删除loguru自动初始化的终端输出
    logger.remove()
    # 日志输出到终端
    logger.add(
        sys.stderr,
        level="INFO",
        format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
               "<level>{level: <8}</level> | "
               "<light-magenta>{thread.name}_{thread.id}</light-magenta> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
    )
    # 日志输出到文件
    logger.add(
        str(log_path / "UITestSchedule_{time:YYYYMMDDHHmmss}.log"),
        level="INFO",
        format="{time:YYYY-MM-DD HH:mm:ss.SSS} | "
               "{level: <8} | "
               "{thread.name}_{thread.id} | "
               "{name}:{function}:{line} - {message}",
        backtrace=False,
        diagnose=False,
        enqueue=True,
        rotation="100 MB",
        retention="15 days",
        compression="tar.gz",
    )


def gen_url_with_user_info(url, username, password):
    # 解析URL
    parsed_url = urlparse(url)

    # 重新构建URL，将用户名和密码添加到netloc部分
    new_netloc = f"{username}:{password}@{parsed_url.netloc}"
    new_url = urlunparse(
        (parsed_url.scheme, new_netloc, parsed_url.path, parsed_url.params, parsed_url.query, parsed_url.fragment))

    if not new_url.endswith(".git"):
        new_url += ".git"
    return new_url


def convert_to_node(py_node):
    node = consumer_pb2.Node()
    node.path = py_node.path
    node.name = py_node.name
    node.alias = py_node.alias
    node.type = py_node.type.value
    node.tags.extend(py_node.tags)
    for child in py_node.children:
        node.children.append(convert_to_node(child))
    return node


def clone_and_parse(**kwargs):
    execute_params = ExecuteParamsModel(**kwargs)
    task_info = ParseTaskModel(**execute_params.task_params.data)
    project_path = os.path.join(execute_params.config.work_space.tmp_path, execute_params.task_params.uuid)
    logger.info(f"project_path: {project_path}")
    try:
        # 获取锁
        redis = Redis(**execute_params.config.redis.model_dump())
        key = f"{const.CONST_PARSE_TASK_PREFIX_LOCK_KEY}::{task_info.project_id}::{task_info.config.config_id}"
        while not redis.acquire(key, str(int(time.time() * 1000))):
            val = redis.get(key)
            if not val:
                continue
            trigger_time = int(val)
            if task_info.trigger_time <= trigger_time:
                logger.info(f"当前触发时间{task_info.trigger_time}比正在处理的时间{trigger_time}早，将丢弃")
                return
            else:
                logger.info(f"当前触发时间{task_info.trigger_time}比正在处理的时间{trigger_time}晚，将稍后重试")
                time.sleep(2)

        # git 操作
        git_url = gen_url_with_user_info(task_info.config.url, "probe", task_info.config.access_token)
        logger.info(f"git_url:{git_url}")

        git_action.get_newest_branch(git_url, task_info.config.branch, project_path)

        # 获取根目录，起始目录
        root_path = start_path = project_path
        logger.info(f"root_path:{root_path}")
        platform_yaml_path = os.path.join(project_path, const.CONST_PY_PLATFORM_INFO_YAML)
        if os.path.exists(platform_yaml_path):
            file_info = common.load_yaml(platform_yaml_path)
            if const.CONST_PY_PLATFORM_INFO_KEY in file_info:
                start_path = os.path.join(root_path, file_info[const.CONST_PY_PLATFORM_INFO_KEY])
                logger.info(f"change start_path:{start_path}")

        # 解析
        pp = PyParser(root_path, start_path)
        pp.parse()
        node = pp.get_root_node().model_dump()
        j = {
            "project_id": task_info.project_id,
            "config_id": task_info.config.config_id,
            "root_node": node,
            "trigger_account": task_info.trigger_account,
            "trigger_time": task_info.trigger_time
        }
        result = json.dumps(j)
        # s = Parse(result, consumer_pb2.ParsePythonProjectTaskResult()).SerializeToString()
        # result.project_id = task_info.project_id
        # result.config_id = task_info.config.config_id
        # result.root_node = convert_to_node(node)
        # result.trigger_account = task_info.trigger_account
        # result.trigger_time = task_info.trigger_time

        # b64 = base64.b64encode(json.dumps(result).encode("utf-8"))
        message = DispatcherTaskModel(
            UUID=execute_params.task_params.uuid,
            Name="handle_parse_python_project_result",
            RoutingKey=execute_params.task_params.routing_key,
            Args=[{
                "Name": "",
                "Type": "[]byte",
                "Value": base64.b64encode(result.encode("utf-8")).decode()
            }]
        )
        job_key = f"Mxjob:{execute_params.task_params.uuid}"
        callback_data = json.dumps(message.model_dump()).encode()
        args = [job_key, const.PARSE_PY_RESULT_QUEUE, callback_data, int(time.time() * (10 ** 9)), execute_params.task_params.uuid]
        # logger.info(f"回调cb：{cb}")
        # logger.info(f"回调信息：{args}")
        redis.zset_get_wait_by_script(const.SCRIPT_PUT_CALLBACK_RESULT, 2, args)

    except Exception as e:
        logger.error(f"执行错误：{e}")
        traceback.print_exc()

    finally:
        logger.info(f"finally: {project_path}")
        if os.path.exists(project_path):
            logger.info(f"os.path.exists {project_path}")
            shutil.rmtree(project_path)


def load_config_file(file_name):
    try:
        with open(file_name, encoding='utf-8') as file:
            conf_dict = yaml.safe_load(file)
    except Exception as error:
        logger.error('读取yaml文件配置异常，error：{}'.format(str(error)))
    return conf_dict


class Consumer:
    def __init__(self):
        register_logging()
        self.config = ConsumerConfigModel(**load_config_file("config.yaml"))
        self.redis = Redis(**self.config.redis.model_dump())
        self.pool = ThreadPoolExecutor(max_workers=5)
        tmp_path = Path(self.config.work_space.tmp_path)
        if not tmp_path.is_dir():
            tmp_path.mkdir(mode=0o755, parents=True)

    def get_task_info(self):
        try:
            items: Union[List[bytes], None] = None

            while items is None:
                args = [const.PARSE_PY_QUEUE, int(time.time() * (10 ** 9)), const.PARSE_PY_TASK_PREFIX]
                items = self.redis.zset_get_wait_by_script(const.SCRIPT_GET_HIGH_SCORE_ITEM, 1, args)
                if items is None:
                    time.sleep(2)

            # SCRIPT_GET_HIGH_SCORE_ITEM 脚本返回None 或者 两个值，分别是任务的key和任务数据
            task_id, task_data = items[0].decode(), items[1].decode()
            task = BaseTaskModel(**json.loads(task_data))
            data = json.loads(base64.b64decode(task.Args[0].Value))
            logger.info(f"DATA: {data}")
            return TaskInfoModel(uuid=task.UUID, name=task.Name, routing_key=task.RoutingKey, data=data)
        except Exception as e:
            logger.warning(f"获取任务信息失败：{e}")
            time.sleep(2)
            return self.get_task_info()

    def listen(self):
        logger.info(f"开始监听")
        while True:
            task = self.get_task_info()
            self.pool.submit(
                clone_and_parse,
                **ExecuteParamsModel(config=self.config, task_params=task).model_dump()
            )


if __name__ == "__main__":
    c = Consumer()
    c.listen()
