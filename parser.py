import ast
from loguru import logger
import os
import re
from _ast import ClassDef, FunctionDef, expr, stmt, AST, Constant
from collections import deque
from typing import List, Dict

import common
import const
from models import AstNode, NodeType


class PyParser:
    path_node_cache: Dict[str, AstNode] = {}

    def __init__(self, root_path, start_path):
        self.root_path = root_path
        self.start_path = start_path

    def parse(self):
        for root, dirs, files in os.walk(self.start_path):
            # 目录
            # 子目录
            remove_dirs = []
            for d in dirs:
                if d == const.CONST_PY_CACHE_DIRECTORY or d.startswith("."):
                    remove_dirs.append(d)
            for r in remove_dirs:
                dirs.remove(r)
            # 当前目录
            if root == const.CONST_PY_CACHE_DIRECTORY or root.startswith("."):
                continue
            self.new_directory_node(root)

            # 文件
            for f in files:
                if f != "." and f.startswith("."):
                    continue
                self.new_file_node(root, f)

    def get_root_node(self):
        path = os.path.relpath(self.start_path, self.root_path)
        return self.path_node_cache[path]

    def new_file_node(self, parent_path: str, file_name: str):
        parent_rel_path = os.path.relpath(parent_path, self.root_path)
        if parent_rel_path in self.path_node_cache:
            parent = self.path_node_cache[parent_rel_path]
            relative_path = os.path.relpath(os.path.join(parent_path, file_name), self.root_path)
            node = AstNode(
                name=file_name,
                path=relative_path,
                type=NodeType.NT_FILE.value
            )

            parent.children.append(node)
            self.path_node_cache[node.path] = node

            # 目录信息文件（解析文件内容，把`folder_alias`的值设置为父节点的别名）
            if file_name == const.CONST_FOLDER_INFO_YAML_FILE:
                file_info = common.load_yaml(os.path.join(parent_path, file_name))
                if const.CONST_FOLDER_ALIAS_KEY in file_info:
                    parent.alias = file_info[const.CONST_FOLDER_ALIAS_KEY]
            # 有`__init__`为包
            elif file_name == const.CONST_PY_INIT_FILE:
                parent.type = NodeType.NT_PACKAGE.value
            elif (file_name.startswith("test_") and file_name.endswith(".py")) or file_name.endswith("_test.py"):
                node.type = NodeType.NT_MODULE.value
                self.ast_parse_py_module(node)

    def new_directory_node(self, path: str):
        name = path.split("/")[-1]
        relative_path = os.path.relpath(path, self.root_path)
        if relative_path == ".":
            name = "."

        node = AstNode(
            name=name,
            path=relative_path,
            type=NodeType.NT_DIRECTORY.value
        )
        self.path_node_cache[relative_path] = node

        if path != self.root_path:
            parent_rel_path = os.path.relpath(os.path.dirname(path), self.root_path)
            if parent_rel_path in self.path_node_cache:
                parent = self.path_node_cache[parent_rel_path]
                parent.children.append(node)

    def get_alias_from_first_line(self, text):
        result = ""
        match = re.search(const.CONST_PY_FILE_ALIAS_PATTERN, text)
        if match:
            result = match.group(1).replace("\n", "")

        return result

    def ast_parse_py_module(self, module: AstNode):
        with open(os.path.join(self.root_path, module.path), 'r') as mf:
            first_line = mf.readline()
            module.alias = self.get_alias_from_first_line(first_line)
            module_text = first_line + mf.read()
            mod = ast.parse(module_text)
            self.ast_walk(module, mod)

    def ast_walk(self, parent: AstNode, obj: AST):
        if isinstance(obj, ast.Module):
            self.ast_walk_stmts(parent, obj.body)
        elif isinstance(obj, ast.ClassDef):
            if obj.name.startswith(const.CONST_PY_TEST_CLASS_PREFIX):
                self.new_class_node(parent, obj)
        elif isinstance(obj, FunctionDef):
            if obj.name.startswith(const.CONST_PY_TEST_FUNCTION_PREFIX):
                self.new_function_node(parent, obj)

    def ast_walk_stmts(self, parent: AstNode, stmts: List[stmt]):
        for s in stmts:
            self.ast_walk(parent, s)

    def new_class_node(self, parent: AstNode, definition: ClassDef):
        name = definition.name
        id = f"{parent.path}::{name}"
        alias, markers = self.parse_decorator_list(definition.decorator_list, const.CONST_PY_CLASS_ALIAS_FUNC_NAME)

        node = AstNode(
            name=name,
            path=id,
            alias=alias,
            type=NodeType.NT_CLASS.value,
            tags=markers
        )

        parent.children.append(node)
        self.path_node_cache[node.path] = node
        self.ast_walk_stmts(node, definition.body)

    def new_function_node(self, parent: AstNode, definition: FunctionDef):
        name = str(definition.name)
        id = f"{parent.path}::{name}"
        alias, markers = self.parse_decorator_list(definition.decorator_list, const.CONST_PY_FUNCTION_ALIAS_FUNC_NAME)

        node = AstNode(
            name=name,
            path=id,
            alias=alias,
            type=NodeType.NT_FUNCTION.value,
            tags=markers
        )

        parent.children.append(node)
        self.path_node_cache[node.path] = node

    def parse_decorator_id(self, e: expr, id_que: deque):
        if isinstance(e, ast.Call):
            self.parse_decorator_id(e.func, id_que)
        elif isinstance(e, ast.Attribute):
            id_que.appendleft(str(e.attr))
            self.parse_decorator_id(e.value, id_que)
        elif isinstance(e, ast.Name):
            id_que.appendleft(str(e.id))
        else:
            logger.debug(f"Unexpected expr type: {type(e)}！！！ please check your code")
        return id_que

    def parse_decorator_list(self, decorator_list: list[expr], function_name: str):
        alias = ""
        markers = []
        for decorator in decorator_list:
            if isinstance(decorator, ast.Call):
                if alias == "":
                    decorator_name = ".".join(self.parse_decorator_id(decorator, deque()))
                    if decorator_name != function_name:
                        continue

                    for arg in decorator.args:
                        if not isinstance(arg, Constant):
                            continue
                        alias = str(arg.s)
            elif isinstance(decorator, ast.Attribute):
                decorator_name = ".".join(self.parse_decorator_id(decorator, deque()))
                if decorator_name.startswith(
                        const.CONST_PY_TEST_MARK_PREFIX) and decorator_name != const.CONST_PY_TEST_MARK_PARAMETRIZE:
                    markers.append(decorator_name[len(const.CONST_PY_TEST_MARK_PREFIX):])

        return alias, markers


if __name__ == "__main__":
    pp = PyParser()
    # pp.ast_parse_py_module()
    pp.parse()
