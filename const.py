CONST_PY_TEST_INI_FILE = "pytest.ini"
CONST_PY_TEST_INI_SECTION = "pytest"
CONST_PY_TEST_INI_SECTION_KEY_TEST_PATHS = "testpaths"
CONST_PY_CACHE_DIRECTORY = "__pycache__"
CONST_PLATFORM_INFO_YAML_FILE = "platform.yaml"
CONST_FOLDER_INFO_YAML_FILE = "folder_info.yaml"
CONST_PY_INIT_FILE = "__init__.py"
CONST_GROUP_NAME_FILE_ALIAS = "file_alias"
CONST_PY_TEST_CLASS_PREFIX = "Test"
CONST_PY_TEST_FUNCTION_PREFIX = "test"
CONST_PY_CLASS_ALIAS_FUNC_NAME = "allure.feature"
CONST_PY_FUNCTION_ALIAS_FUNC_NAME = "allure.title"
CONST_PY_TEST_MARK_PREFIX = "pytest.mark."
CONST_PY_TEST_MARK_PARAMETRIZE = CONST_PY_TEST_MARK_PREFIX + "parametrize"
CONST_FOLDER_ALIAS_KEY = "folder_alias"
CONST_PY_FILE_ALIAS_PATTERN = r"# textfile_alias = (.+)"
CONST_PY_PLATFORM_INFO_YAML = "platform.yaml"
CONST_PY_PLATFORM_INFO_KEY = "test_folder_path"
CONST_PARSE_TASK_PREFIX_LOCK_KEY = "lock:parsePythonProjectTask:projectID:configID"

PARSE_PY_QUEUE = "mqc:ui_worker"
PARSE_PY_RESULT_QUEUE = "mq:parse_python_project_task"
PARSE_PY_TASK_PREFIX = "Mxjob:"

SCRIPT_GET_HIGH_SCORE_ITEM = """
local member = redis.call("ZRANGEBYSCORE", KEYS[1], "-inf", ARGV[1], "LIMIT", 0, 1)[1]

if member then
  local key = ARGV[2] .. member
  local val = redis.call('GET', key)
  redis.call('ZREM', KEYS[1], member)
  redis.call('DEL', key)
  return {member, val}
end

return nil
"""

SCRIPT_PUT_CALLBACK_RESULT = """
local ok = redis.call("SET", KEYS[1], ARGV[1], "NX")
if not ok then
  return -1 
end
if redis.call("ZADD", KEYS[2], ARGV[2], ARGV[3]) ~= 1 then
  return 0
end
return 1
"""