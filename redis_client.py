import time
from typing import Union, Awaitable

import redis


class Redis(object):

    def __init__(self, **redis_kwargs):
        self.__db = redis.Redis(**redis_kwargs)

    def remove(self, key: str):
        return self.__db.delete(key)

    def search(self, key):
        return self.__db.keys(key)

    def qsize(self, key: str):
        return self.__db.llen(key)

    def put(self, key: str, item):
        self.__db.rpush(key, item)

    def get_wait(self, key: str, timeout=None):
        # 返回队列第一个元素，如果为空则等待至有元素被加入队列（超时时间阈值为timeout，如果为None则一直等待）
        item = self.__db.blpop([key], timeout=timeout)

        return item

    def get_nowait(self, key: str):
        # 直接返回队列第一个元素，如果队列为空返回的是None
        item = self.__db.lpop(key)
        return item

    def zset_get_wait(self, key: str):
        item = None
        while item is None:
            item = self.__db.zpopmax(key)
            time.sleep(1)

        return item

    def zset_get_wait_by_script(self, script: str, key_count: int, key_and_args: list, times: int = 0) -> Union[
        Awaitable[str], str]:
        result = self.__db.eval(script, key_count, *key_and_args)
        return result

    def get(self, key) -> Union[str, None]:
        v = self.__db.get(key)
        if type(v) == bytes:
            return v.decode("utf-8")
        else:
            return None

    def set(self, name, value, timeout=15, **kwargs) -> Union[str, None]:
        return self.__db.set(name=name, value=value, ex=timeout, **kwargs)

    def acquire(self, key, value="1", timeout=15):
        result = self.__db.setnx(key, value)
        if result:
            self.__db.expire(key, timeout)
        return result

    def release(self, key):
        self.__db.delete(key)  # 直接使用Redis的delete命令删除key。
