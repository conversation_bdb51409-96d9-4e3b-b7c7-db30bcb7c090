import os

from git import Repo


def get_newest_branch(git_url, branch, to_path):
    if os.path.exists(to_path):
        repo = Repo(to_path)
        repo.git.reset('--hard', 'HEAD')
        repo.git.clean('-xdf')
        repo.git.stash("save")
        repo.git.fetch()
        repo.git.checkout(branch)
        repo.git.pull("--rebase")
        repo.git.stash("clear")
    else:
        os.makedirs(to_path)
        Repo.clone_from(url=git_url, to_path=to_path, branch=branch)
