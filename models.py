from enum import Enum
from typing import Optional, List

from pydantic import BaseModel


class NodeType(Enum):
    NT_NULL = 0
    NT_DIRECTORY = 1
    NT_FILE = 2
    NT_PACKAGE = 3
    NT_MODULE = 4
    NT_CLASS = 5
    NT_FUNCTION = 6


class AstNode(BaseModel):
    path: Optional[str] = ""
    name: Optional[str] = ""
    alias: Optional[str] = ""
    type: Optional[int] = 0
    tags: Optional[List[str]] = []
    children: Optional[List["AstNode"]] = []


class RedisConfigModel(BaseModel):
    host: str
    port: int = 6379
    db: int
    password: Optional[str] = None


class WorkSpaceConfigModel(BaseModel):
    tmp_path: str


class ConsumerConfigModel(BaseModel):
    redis: RedisConfigModel
    work_space: WorkSpaceConfigModel


class BaseTaskArgsModel(BaseModel):
    Name: str
    Type: str
    Value: str


class BaseTaskModel(BaseModel):
    """ 只列举了task部分关键参数 """
    UUID: str
    Name: str
    RoutingKey: str
    Args: list[BaseTaskArgsModel]


class TaskInfoModel(BaseModel):
    """ 任务参数模型 """
    # task 参数
    uuid: str
    routing_key: str
    name: str
    data: dict


class ExecuteParamsModel(BaseModel):
    """ 防止重名，中转使用，具体类型交给worker决定 """
    config: ConsumerConfigModel
    task_params: TaskInfoModel


class ParseTaskConfigModel(BaseModel):
    project_id: str
    config_id: str
    type: str
    name: str
    description: str
    url: str
    access_token: str
    branch: str


class ParseTaskModel(BaseModel):
    project_id: str
    config: ParseTaskConfigModel
    trigger_mode: int
    trigger_account: str
    trigger_time: int


class DispatcherTaskModel(BaseModel):
    UUID: str = ""
    Name: str = ""
    RoutingKey: str = ""
    ETA: None = None
    GroupUUID: str = ""
    GroupTaskCount: int = 0
    Headers: dict = {}
    Priority: int = 0
    Immutable: bool = False
    RetryCount: int = 0
    RetryTimeout: int = 0
    OnSuccess: None = None
    OnError: None = None
    ChordCallback: None = None
    BrokerMessageGroupId: str = ""
    SQSReceiptHandle: str = ""
    StopTaskDeletionOnError: bool = False
    IgnoreWhenTaskNotRegistered: bool = False
    Args: list